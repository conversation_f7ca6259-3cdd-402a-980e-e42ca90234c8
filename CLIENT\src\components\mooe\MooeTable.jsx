import React, { useCallback, useMemo, useState, useEffect } from "react";
import { useRegion } from "../../context/RegionContext";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableFooter,
  Paper,
  Box,
  CircularProgress,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Switch,
  FormControlLabel,
  Tooltip,
  Menu,
  MenuItem,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormGroup,
  Divider,
  Alert,
  Snackbar,
  Zoom,
  Fade,
} from "@mui/material";
import { ToastContainer, toast } from "react-toastify";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import StickyButtons from "./StickyButtons";
import MooeRow from "./MooeRow";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import "react-toastify/dist/ReactToastify.css";
import { NumericFormat } from "react-number-format";

// Custom component for numeric input formatting - memoized for performance
const NumberFormatCustom = React.memo(React.forwardRef(function NumberFormatCustom(props, ref) {
  const { onChange, ...other } = props;

  // Memoize the onValueChange callback to prevent unnecessary re-renders
  const handleValueChange = React.useCallback((values) => {
    // Preserve empty values instead of converting to "0"
    // This allows users to clear fields without auto-filling with zero
    const numericValue = values.value === undefined || values.value === "" ? "" : values.value;
    onChange({
      target: {
        value: numericValue,
      },
    });
  }, [onChange]);

  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      thousandSeparator
      decimalScale={2}
      fixedDecimalScale={false} // Don't force decimal places
      allowNegative={false} // Prevent negative values
      onValueChange={handleValueChange}
    />
  );
}));
// Import icons
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import GetAppIcon from "@mui/icons-material/GetApp";
import SaveIcon from "@mui/icons-material/Save";
import ClearIcon from "@mui/icons-material/Clear";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import BarChartIcon from "@mui/icons-material/BarChart";

const Mooe = ({ onDataChange }) => {
  const { currentUser } = useUser();
  const { activeRegion } = useRegion(); // Get the active region from context
  const queryClient = useQueryClient();
  const [expandedRows, setExpandedRows] = useState([]);
  const [disableIncomeInputs, setDisableIncomeInputs] = useState(false);

  // Debug user and region loading
  useEffect(() => {
    console.log("Mooe component mounted/updated:", { currentUser, activeRegion });
  }, [currentUser, activeRegion]);

  // Enhanced state management
  const [searchTerm, setSearchTerm] = useState("");
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState({
    sublineItems: [],
    amountRange: { min: "", max: "" },
    hasValues: false,
    emptyValues: false
  });
  const [autoSave, setAutoSave] = useState(false); // Default to false as requested
  const [bulkEditMode, setBulkEditMode] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [viewMode, setViewMode] = useState("table"); // table, chart, summary
  const [showMiniCharts, setShowMiniCharts] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [autoSaveTimer, setAutoSaveTimer] = useState(null);

  // Fetch data using React Query
  const { data, isLoading, error } = useQuery({
    queryKey: ["mooeData", activeRegion?.id], // Add region ID to query key to refetch when region changes
    queryFn: async () => {
      try {
        // Add region parameter to the API request
        const params = activeRegion?.id ? { region: activeRegion.id } : {};
        const response = await api.get("/mooe-data", {
          params,
          withCredentials: true
        });

        // Validate response structure
        if (!response.data) {
          throw new Error("No data received from server");
        }

        const { entries, status, settings } = response.data;

        // Validate entries array
        if (!Array.isArray(entries)) {
          console.warn("Entries is not an array, using empty array");
          return {
            formattedData: [],
            status: status || "Not Submitted",
            fiscalYear: settings?.fiscalYear || "",
            budgetType: settings?.budgetType || "",
          };
        }

        const formatted = formatData(entries);

        return {
          formattedData: formatted,
          status: status || "Not Submitted",
          fiscalYear: settings?.fiscalYear || "",
          budgetType: settings?.budgetType || "",
        };
      } catch (err) {
        console.error("Error in MOOE data query:", err);
        // Re-throw to trigger onError
        throw err;
      }
    },
    onError: (err) => {
      console.error("Error fetching MOOE data:", err);
      const errorMessage = err.response?.data?.message || err.message || "Failed to load data";
      toast.error(`Failed to load MOOE data: ${errorMessage}`);
    },
    // Add retry logic
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors (client errors)
      if (error.response?.status >= 400 && error.response?.status < 500) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
  
  // Fetch IAs O&M Cost data
  const { data: iasOMCostData } = useQuery({
    queryKey: ["iasOMCostData", activeRegion?.id], // Add region ID to query key
    queryFn: async () => {
      try {
        // Add region parameter to the API request
        const params = activeRegion?.id ? { region: activeRegion.id } : {};
        const response = await api.get("/ias-om-cost", {
          params,
          withCredentials: true
        });

        // Validate response data
        const data = response.data || {};
        return {
          nis: Number(data.nis) || 0,
          cis: Number(data.cis) || 0,
          nisSubsidy: Number(data.nisSubsidy) || 0,
          cisSubsidy: Number(data.cisSubsidy) || 0
        };
      } catch (err) {
        console.warn("IAs O&M Cost endpoint not available or error occurred:", err.message);
        // If the endpoint doesn't exist yet, return default values
        return { nis: 0, cis: 0, nisSubsidy: 0, cisSubsidy: 0 };
      }
    },
    // Don't show error toast for this query since it's optional
    onError: () => {
      // Silent fail - this is expected if the endpoint doesn't exist
    }
  });
  
  // State for IAs O&M Cost values
  const [iasOMCost, setIasOMCost] = useState({
    nis: "0",
    cis: "0",
    nisSubsidy: "0",
    cisSubsidy: "0"
  });

  // Save mutation
  const saveMutation = useMutation({
    mutationFn: (payload) => {
      console.log("saveMutation.mutationFn called with payload:", payload);
      // Include the region ID in the payload
      if (activeRegion?.id) {
        payload.region = activeRegion.id;
      }
      console.log("Making API call to /mooe-save");
      return api.post("/mooe-save", payload, { withCredentials: true });
    },
    onError: (err) => {
      console.error("Save mutation failed:", err);
      console.error("Error details:", err.response?.data || err.message);
      toast.error("Failed to save data.");
    },
  });

  // Memoize formatData function to prevent unnecessary recalculations
  const formatData = useCallback((rawData) => {
    if (!Array.isArray(rawData) || rawData.length === 0) {
      return [];
    }

    // Group by sublineItem but keep all accounting titles visible
    const grouped = {};
    for (let idx = 0; idx < rawData.length; idx++) {
      const item = rawData[idx];
      const key = item.sublineItem || `unknown-${idx}`;
      if (!grouped[key]) {
        grouped[key] = {
          id: key,
          sublineItem: item.sublineItem,
          children: [],
        };
      }
      grouped[key].children.push({
        ...item,
        id: `${key}-${item.uacsCode || "none"}-${idx}`,
      });
    }

    return Object.values(grouped);
  }, []);  // Calculate grand totals including IAs O&M Cost
  const grandTotals = useMemo(() => {
    if (!data?.formattedData) return {
      regularIncome: 0,
      regularSubsidy: 0,
      regularTotal: 0,
      income: 0,
      subsidy: 0,
      total: 0,
      iasIncomeTotal: 0,
      iasSubsidyTotal: 0,
      iasTotal: 0,
      ias: {
        nis: 0,
        cis: 0,
        nisSubsidy: 0,
        cisSubsidy: 0,
        total: 0
      }
    };

    // Calculate regular MOOE totals
    let regularIncome = 0;
    let regularSubsidy = 0;

    data.formattedData.forEach(row => {
      row.children.forEach(child => {
        // For regular MOOE, only include if it's not a special IAs O&M UACS code
        if (child.uacsCode !== "5-02-99-990-NIS" && child.uacsCode !== "5-02-99-990-CIS") {
          regularIncome += parseFloat(child.income || 0);
          regularSubsidy += parseFloat(child.subsidy || 0);
        }
      });
    });

    // Calculate IAs O&M Cost totals from dedicated fields
    const iasNIS = parseFloat(iasOMCost.nis || 0);
    const iasCIS = parseFloat(iasOMCost.cis || 0);
    const iasNISSubsidy = parseFloat(iasOMCost.nisSubsidy || 0);
    const iasCISSubsidy = parseFloat(iasOMCost.cisSubsidy || 0);

    const iasIncomeTotal = iasNIS + iasCIS;
    const iasSubsidyTotal = iasNISSubsidy + iasCISSubsidy;
    const iasTotal = iasIncomeTotal + iasSubsidyTotal;

    // Calculate combined totals
    const totalIncome = regularIncome + iasIncomeTotal;
    const totalSubsidy = regularSubsidy + iasSubsidyTotal;
    const regularTotal = regularIncome + regularSubsidy;
    const total = totalIncome + totalSubsidy;
    
    return { 
      regularIncome,
      regularSubsidy,
      regularTotal,
      income: totalIncome, 
      subsidy: totalSubsidy, 
      total,
      iasIncomeTotal,
      iasSubsidyTotal,
      iasTotal,
      ias: {
        nis: iasNIS,
        cis: iasCIS,
        nisSubsidy: iasNISSubsidy,
        cisSubsidy: iasCISSubsidy,
        total: iasTotal
      }
    };
  }, [data?.formattedData, iasOMCost]);

  // Keep all rows collapsed by default for better performance
  // Removed auto-expand functionality to improve loading speed

  // Calculate total function (moved up to avoid hoisting issues)
  const calculateTotal = useCallback((children) => {
    return children.reduce((sum, child) => {
      const income = parseFloat(child.income || 0);
      const subsidy = parseFloat(child.subsidy || 0);
      return sum + income + subsidy;
    }, 0);
  }, []);

  // Enhanced filtering logic
  const filteredData = useMemo(() => {
    if (!data?.formattedData) return [];

    return data.formattedData.filter(row => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSubline = row.sublineItem?.toLowerCase().includes(searchLower);
        const matchesChildren = row.children.some(child =>
          child.accountingTitle?.toLowerCase().includes(searchLower) ||
          child.uacsCode?.toLowerCase().includes(searchLower)
        );
        if (!matchesSubline && !matchesChildren) return false;
      }

      // Subline item filter
      if (selectedFilters.sublineItems.length > 0) {
        if (!selectedFilters.sublineItems.includes(row.sublineItem)) return false;
      }

      // Amount range filter
      if (selectedFilters.amountRange.min || selectedFilters.amountRange.max) {
        const totalAmount = calculateTotal(row.children);
        const min = parseFloat(selectedFilters.amountRange.min) || 0;
        const max = parseFloat(selectedFilters.amountRange.max) || Infinity;
        if (totalAmount < min || totalAmount > max) return false;
      }

      // Has values filter
      if (selectedFilters.hasValues) {
        const hasValues = row.children.some(child =>
          parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0
        );
        if (!hasValues) return false;
      }

      // Empty values filter
      if (selectedFilters.emptyValues) {
        const hasValues = row.children.some(child =>
          parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0
        );
        if (hasValues) return false;
      }

      return true;
    });
  }, [data?.formattedData, searchTerm, selectedFilters, calculateTotal]);

  // Auto-save functionality - moved after handleSave definition
  // This will be handled by the handleSave function itself
  
  // Enhanced IAs O&M Cost state synchronization
  useEffect(() => {
    if (iasOMCostData) {
      console.log("Syncing IAs O&M Cost data:", iasOMCostData);
      setIasOMCost(prevState => {
        const newState = {
          nis: iasOMCostData.nis ? iasOMCostData.nis.toString() : "0",
          cis: iasOMCostData.cis ? iasOMCostData.cis.toString() : "0",
          nisSubsidy: iasOMCostData.nisSubsidy ? iasOMCostData.nisSubsidy.toString() : "0",
          cisSubsidy: iasOMCostData.cisSubsidy ? iasOMCostData.cisSubsidy.toString() : "0"
        };

        // Only update if there are actual changes to prevent unnecessary re-renders
        const hasChanges = Object.keys(newState).some(key => newState[key] !== prevState[key]);
        if (hasChanges) {
          console.log("IAs O&M Cost state updated:", newState);
          return newState;
        }

        return prevState;
      });
    }
  }, [iasOMCostData]);
  
  // Enhanced data refetch logic when region changes
  useEffect(() => {
    if (activeRegion?.id) {
      console.log("Region changed, refetching data for:", activeRegion.id);

      // Reset local state to prevent stale data
      setHasUnsavedChanges(false);
      setValidationErrors({});
      setExpandedRows([]);

      // Invalidate and refetch queries with new region
      Promise.all([
        queryClient.invalidateQueries(["mooeData", activeRegion.id]),
        queryClient.invalidateQueries(["iasOMCostData", activeRegion.id])
      ]).then(() => {
        console.log("Data refetch completed for region:", activeRegion.id);
      }).catch((error) => {
        console.error("Error during data refetch:", error);
        toast.error("Failed to load data for the selected region");
      });
    }
  }, [activeRegion?.id, queryClient]);

  // Track unsaved changes
  const trackChanges = useCallback(() => {
    console.log("trackChanges called - setting hasUnsavedChanges to true");
    setHasUnsavedChanges(true);
  }, []);

  const toggleExpandRow = useCallback((rowId) => {
    setExpandedRows(prev => 
      prev.includes(rowId) 
        ? prev.filter(id => id !== rowId)
        : [...prev, rowId]
    );
  }, []);

  const handleAmountChange = useCallback((rowId, childId, value) => {
    // Validate input
    const numValue = parseFloat(value) || 0;
    if (numValue < 0) {
      setValidationErrors(prev => ({
        ...prev,
        [`${rowId}-${childId}-amount`]: "Amount cannot be negative"
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${rowId}-${childId}-amount`];
        return newErrors;
      });
    }

    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;

      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;

          return {
            ...row,
            children: row.children.map(child =>
              child.id === childId ? { ...child, amount: value } : child
            )
          };
        })
      };
    });

    trackChanges();
  }, [queryClient, trackChanges]);

  const handleIncomeChange = useCallback((rowId, childId, value) => {
    console.log("🔥 handleIncomeChange called:", { rowId, childId, value, valueType: typeof value });

    // If income inputs are disabled, don't allow changes
    if (disableIncomeInputs) {
      toast.warning("Income fields are locked because the total matches the Corporate Income projection. To make changes, adjust the Corporate Income first.");
      return;
    }

    // Don't convert empty string to 0 - preserve the actual input
    const stringValue = value === "" ? "" : value.toString().trim();
    const numValue = parseFloat(stringValue);

    console.log("Processing income change:", { stringValue, numValue });

    // Enhanced validation with better error messages
    if (stringValue !== "") {
      if (isNaN(numValue)) {
        setValidationErrors(prev => ({
          ...prev,
          [`${rowId}-${childId}-income`]: "Please enter a valid number for income"
        }));
        return;
      }

      if (numValue < 0) {
        setValidationErrors(prev => ({
          ...prev,
          [`${rowId}-${childId}-income`]: "Income cannot be negative"
        }));
        return;
      }

      // Check for extremely large values
      if (numValue > 999999999999) {
        setValidationErrors(prev => ({
          ...prev,
          [`${rowId}-${childId}-income`]: "Income value is too large"
        }));
        return;
      }
    }

    // Clear validation errors if value is valid
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[`${rowId}-${childId}-income`];
      return newErrors;
    });

    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;

      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;

          return {
            ...row,
            children: row.children.map(child => {
              if (child.id === childId) {
                const newIncome = stringValue === "" ? "0" : stringValue;
                const newAmount = (parseFloat(stringValue || 0) + parseFloat(child.subsidy || 0)).toString();
                console.log("Updating income:", {
                  childId,
                  oldIncome: child.income,
                  newIncome,
                  subsidy: child.subsidy,
                  newAmount
                });
                return {
                  ...child,
                  income: newIncome,
                  // Auto-compute amount when income changes
                  amount: newAmount
                };
              }
              return child;
            })
          };
        })
      };
    });

    trackChanges();
    console.log("Income change completed, hasUnsavedChanges should be true");
  }, [queryClient, disableIncomeInputs, trackChanges, autoSave, autoSaveTimer]);

  const handleSubsidyChange = useCallback((rowId, childId, value) => {
    console.log("🔥 handleSubsidyChange called:", { rowId, childId, value, valueType: typeof value });

    // Don't convert empty string to 0 - preserve the actual input
    const stringValue = value === "" ? "" : value.toString().trim();
    const numValue = parseFloat(stringValue);

    console.log("Processing subsidy change:", { stringValue, numValue });

    // Enhanced validation with better error messages
    if (stringValue !== "") {
      if (isNaN(numValue)) {
        setValidationErrors(prev => ({
          ...prev,
          [`${rowId}-${childId}-subsidy`]: "Please enter a valid number for subsidy"
        }));
        return;
      }

      if (numValue < 0) {
        setValidationErrors(prev => ({
          ...prev,
          [`${rowId}-${childId}-subsidy`]: "Subsidy cannot be negative"
        }));
        return;
      }

      // Check for extremely large values
      if (numValue > 999999999999) {
        setValidationErrors(prev => ({
          ...prev,
          [`${rowId}-${childId}-subsidy`]: "Subsidy value is too large"
        }));
        return;
      }
    }

    // Clear validation errors if value is valid
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[`${rowId}-${childId}-subsidy`];
      return newErrors;
    });

    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;

      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;

          return {
            ...row,
            children: row.children.map(child => {
              if (child.id === childId) {
                const newSubsidy = stringValue === "" ? "0" : stringValue;
                const newAmount = (parseFloat(child.income || 0) + parseFloat(stringValue || 0)).toString();
                console.log("Updating subsidy:", {
                  childId,
                  oldSubsidy: child.subsidy,
                  newSubsidy,
                  income: child.income,
                  newAmount
                });

                console.log("🔍 Updated child object:", {
                  ...child,
                  subsidy: newSubsidy,
                  amount: newAmount
                });
                return {
                  ...child,
                  subsidy: newSubsidy,
                  // Auto-compute amount when subsidy changes
                  amount: newAmount
                };
              }
              return child;
            })
          };
        })
      };
    });

    // Log the updated state
    const updatedData = queryClient.getQueryData(["mooeData"]);
    console.log("🔍 Current state after subsidy update:", updatedData?.formattedData?.find(r => r.id === rowId)?.children?.find(c => c.id === childId));

    trackChanges();
    console.log("Subsidy change completed, hasUnsavedChanges should be true");
  }, [queryClient, trackChanges]);

  const handleTitleChange = useCallback((rowId, childId, value) => {
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          
          return {
            ...row,
            children: row.children.map(child => 
              child.id === childId ? { ...child, accountingTitle: value } : child
            )
          };
        })
      };
    });
  }, [queryClient]);
  
  // Handlers for IAs O&M Cost input fields
  const handleIAsOMCostChange = (field) => (event) => {
    let value = event.target.value;
    
    // Don't convert empty string to 0 - preserve the actual input
    const stringValue = value === "" ? "" : value;
    const numValue = parseFloat(stringValue);
    
    // Only validate if there's actually a value
    if (stringValue !== "" && (isNaN(numValue) || numValue < 0)) {
      setValidationErrors(prev => ({
        ...prev,
        [`ias-${field}`]: `${field.toUpperCase()} must be a valid positive number`
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`ias-${field}`];
        return newErrors;
      });
    }
    
    setIasOMCost(prev => ({ ...prev, [field]: stringValue === "" ? "0" : stringValue }));
    setHasUnsavedChanges(true);
  };

  const addCustomMOOE = useCallback((rowId) => {
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          
          // Find the Other MOOE category to get its details
          const otherMooeItem = row.children.find(child => child.uacsCode === "5-02-99-990");
          
          if (!otherMooeItem) return row;
          
          return {
            ...row,
            children: [
              ...row.children,
              {
                id: `${rowId}-custom-${Date.now()}`,
                accountingTitle: "Custom MOOE",
                uacsCode: "5-02-99-990",
                amount: "0",
                income: "0",
                subsidy: "0",
                custom: true,
                sublineItem: row.sublineItem
              }
            ]
          };
        })
      };
    });
  }, [queryClient]);

  const handleSave = (isAutoSave = false) => {
    console.log("handleSave called:", { isAutoSave, currentUser, activeRegion });

    // Enhanced validation checks
    if (!data?.formattedData) {
      console.log("No data to save");
      if (!isAutoSave) {
        toast.warning("No data available to save.");
      }
      return;
    }

    // Check if currentUser is available
    if (!currentUser) {
      console.error("currentUser is not available");
      if (!isAutoSave) {
        toast.error("User information not available. Please refresh the page and try again.");
      }
      return;
    }

    // Check for validation errors with detailed feedback
    if (Object.keys(validationErrors).length > 0) {
      console.log("Validation errors found:", validationErrors);
      if (!isAutoSave) {
        const errorCount = Object.keys(validationErrors).length;
        const firstError = Object.values(validationErrors)[0];
        toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? 's' : ''} before saving. First error: ${firstError}`);
      }
      return;
    }

    // Check if activeRegion is available
    if (!activeRegion?.id) {
      console.error("activeRegion is not available");
      if (!isAutoSave) {
        toast.error("Region information not available. Please select a region and try again.");
      }
      return;
    }
    
    // Get the current data from queryClient
    const currentData = queryClient.getQueryData(["mooeData", activeRegion?.id]);
    
    // If income inputs are disabled, preserve the original income values
    let entries;
    if (disableIncomeInputs && currentData?.formattedData) {
      // Create a map of original income values
      const originalIncomeMap = {};
      currentData.formattedData.forEach(row => {
        row.children.forEach(child => {
          const key = `${row.id}-${child.id}`;
          originalIncomeMap[key] = child.income;
        });
      });
      
      // Use original income values but updated subsidy values
      entries = data.formattedData
        .flatMap(row =>
          row.children.map(child => {
            const key = `${row.id}-${child.id}`;
            const originalIncome = originalIncomeMap[key] || child.income;

            // Determine if this is a new entry (has values but no existing status)
            const hasValues = parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0;
            const isNewEntry = hasValues && (!child.status || child.status === "");
            const originalStatus = child.status;

            // Enhanced status logic with better tracking
            let newStatus;
            if (isNewEntry) {
              newStatus = "Not Submitted";
              console.log(`New entry detected: ${child.accountingTitle} -> Status: "Not Submitted"`);
            } else if (child.status) {
              newStatus = child.status; // Keep existing status
              console.log(`Existing entry: ${child.accountingTitle} -> Status: "${child.status}"`);
            } else {
              newStatus = data?.status || "Not Submitted"; // Fallback
              console.log(`Fallback status for: ${child.accountingTitle} -> Status: "${newStatus}"`);
            }

            return {
              ...child,
              sublineItem: row.sublineItem,
              status: newStatus,
              originalStatus: originalStatus, // Track original status for debugging
              income: disableIncomeInputs ? parseFloat(originalIncome || 0) : parseFloat(child.income || 0),
              subsidy: parseFloat(child.subsidy || 0),
              amount: disableIncomeInputs
                ? parseFloat(originalIncome || 0) + parseFloat(child.subsidy || 0)
                : parseFloat(child.income || 0) + parseFloat(child.subsidy || 0),
              // Ensure we have required fields for server validation
              uacsCode: child.uacsCode || "UNKNOWN",
              accountingTitle: child.accountingTitle || "Unknown Account",
              // Preserve the ID if it exists (important for updates vs inserts)
              id: child.id || undefined
            };
          })
        )
        .filter(entry => {
          // Include entries that have values, have been modified, or are system entries
          const hasValues = parseFloat(entry.income || 0) !== 0 || parseFloat(entry.subsidy || 0) !== 0;
          const hasExistingId = entry.id && entry.id !== 'undefined' && entry.id !== '';
          const isSystemEntry = entry.isSystemEntry;

          // Only include entries that have values, existing IDs (previously saved), or are system entries
          const shouldInclude = hasValues || hasExistingId || isSystemEntry;

          console.log(`Filter check for ${entry.uacsCode}: hasValues=${hasValues}, hasExistingId=${hasExistingId}, isSystemEntry=${isSystemEntry}, shouldInclude=${shouldInclude}`);

          return shouldInclude;
        });
    } else {
      // Normal processing if income inputs are not disabled
      entries = data.formattedData
        .flatMap(row =>
          row.children.map(child => {
            const hasValues = parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0;
            const isNewEntry = hasValues && (!child.status || child.status === "");
            const originalStatus = child.status;

            // Enhanced status logic with better tracking
            let newStatus;
            if (isNewEntry) {
              newStatus = "Not Submitted";
              console.log(`New entry detected: ${child.accountingTitle} -> Status: "Not Submitted"`);
            } else if (child.status) {
              newStatus = child.status; // Keep existing status
              console.log(`Existing entry: ${child.accountingTitle} -> Status: "${child.status}"`);
            } else {
              newStatus = data?.status || "Not Submitted"; // Fallback
              console.log(`Fallback status for: ${child.accountingTitle} -> Status: "${newStatus}"`);
            }

            return {
              ...child,
              sublineItem: row.sublineItem,
              status: newStatus,
              originalStatus: originalStatus, // Track original status for debugging
              income: parseFloat(child.income || 0),
              subsidy: parseFloat(child.subsidy || 0),
              amount: parseFloat(child.income || 0) + parseFloat(child.subsidy || 0),
              // Ensure we have required fields for server validation
              uacsCode: child.uacsCode || "UNKNOWN",
              accountingTitle: child.accountingTitle || "Unknown Account",
              // Preserve the ID if it exists (important for updates vs inserts)
              id: child.id || undefined
            };
          })
        )
        .filter(entry => {
          // Include entries that have values, have been modified, or are system entries
          const hasValues = parseFloat(entry.income || 0) !== 0 || parseFloat(entry.subsidy || 0) !== 0;
          const hasExistingId = entry.id && entry.id !== 'undefined' && entry.id !== '';
          const isSystemEntry = entry.isSystemEntry;

          // Only include entries that have values, existing IDs (previously saved), or are system entries
          const shouldInclude = hasValues || hasExistingId || isSystemEntry;

          console.log(`Filter check for ${entry.uacsCode}: hasValues=${hasValues}, hasExistingId=${hasExistingId}, isSystemEntry=${isSystemEntry}, shouldInclude=${shouldInclude}`);

          return shouldInclude;
        });
    }

    // Add IAs O&M Cost entries if they have values
    const iasOMCostEntries = [];
    
    // Add NIS entry if it has values
    if (parseFloat(iasOMCost.nis || 0) > 0 || parseFloat(iasOMCost.nisSubsidy || 0) > 0) {
      iasOMCostEntries.push({
        sublineItem: "Irrigators' Associations (IAs) Operation & Maintenance Cost",
        accountingTitle: "National Irrigation System",
        uacsCode: "5-02-99-990-NIS",
        income: parseFloat(iasOMCost.nis || 0),
        subsidy: parseFloat(iasOMCost.nisSubsidy || 0),
        amount: parseFloat(iasOMCost.nis || 0) + parseFloat(iasOMCost.nisSubsidy || 0),
        status: data?.status || "Not Submitted"
      });
    }
    
    // Add CIS entry if it has values
    if (parseFloat(iasOMCost.cis || 0) > 0 || parseFloat(iasOMCost.cisSubsidy || 0) > 0) {
      iasOMCostEntries.push({
        sublineItem: "Irrigators' Associations (IAs) Operation & Maintenance Cost",
        accountingTitle: "Communal Irrigation System",
        uacsCode: "5-02-99-990-CIS",
        income: parseFloat(iasOMCost.cis || 0),
        subsidy: parseFloat(iasOMCost.cisSubsidy || 0),
        amount: parseFloat(iasOMCost.cis || 0) + parseFloat(iasOMCost.cisSubsidy || 0),
        status: data?.status || "Not Submitted"
      });
    }

    const payload = {
      meta: {
        processBy: `${currentUser.FirstName} ${currentUser.LastName}`,
        processDate: new Date(),
        region: activeRegion?.id || currentUser.Region, // Use the active region from context
        fiscalYear: data?.fiscalYear,
        budgetType: data?.budgetType,
        status: data?.status,
      },
      entries: [...entries, ...iasOMCostEntries],
    };

    console.log("=== SAVE DEBUG INFO ===");
    console.log("Raw entries before filtering:", data.formattedData?.flatMap(row => row.children).length || 0);
    console.log("Filtered entries count:", entries.length);
    console.log("IAs O&M Cost entries count:", iasOMCostEntries.length);
    console.log("Total entries to save:", payload.entries.length);
    console.log("Sample entries:", entries.slice(0, 3));
    console.log("Sample entry structure:", entries[0]);
    console.log("Payload meta:", payload.meta);
    console.log("=== END DEBUG INFO ===");

    // Check if we have any entries to save
    if (payload.entries.length === 0) {
      console.warn("No entries to save - this might cause a server error");

      // If no entries, create a minimal entry to prevent server error
      // This can happen when all values are zero but we still want to save the state
      payload.entries = [{
        sublineItem: "System Entry",
        accountingTitle: "Placeholder Entry",
        uacsCode: "SYSTEM",
        income: 0,
        subsidy: 0,
        amount: 0,
        status: data?.status || "Not Submitted",
        isSystemEntry: true // Flag to identify this as a system-generated entry
      }];

      console.log("Added system entry to prevent save error");
    }

    // Save all MOOE data including IAs O&M Cost entries
    saveMutation.mutate(payload, {
      onSuccess: (response) => {
        console.log("Save response:", response.data);
        setHasUnsavedChanges(false);
        setLastSaved(new Date());

        if (isAutoSave) {
          toast.success("Auto-saved successfully!", { autoClose: 2000 });
        } else {
          toast.success("Data saved successfully!");
        }

        // Refresh the data after saving
        setTimeout(() => {
          queryClient.invalidateQueries(["mooeData"]);
          queryClient.invalidateQueries(["consolidatedSummary"]);
        }, 500);
      },
      onError: (error) => {
        console.error("Save error:", error);
        if (!isAutoSave) {
          toast.error(`Failed to save: ${error.message || 'Unknown error'}`);
        }
      }
    });
  };

  const handleClear = () => {
    queryClient.invalidateQueries(["mooeData"]);
  };

  // Enhanced auto-save functionality with better debouncing and error handling
  useEffect(() => {
    console.log("Auto-save useEffect triggered:", { autoSave, hasUnsavedChanges });

    // Clear existing timer
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
      setAutoSaveTimer(null);
    }

    if (autoSave && hasUnsavedChanges) {
      console.log("Setting up auto-save timer...");

      const timer = setTimeout(async () => {
        try {
          console.log("Auto-save timer triggered, calling handleSave...");
          await handleSave(true); // true indicates auto-save
          console.log("Auto-save completed successfully");
        } catch (error) {
          console.error("Auto-save failed:", error);
          // Don't show error toast for auto-save failures to avoid spam
          // The user can manually save if needed
        }
      }, 5000); // Auto-save after 5 seconds of inactivity (increased for better UX)

      setAutoSaveTimer(timer);

      return () => {
        clearTimeout(timer);
        setAutoSaveTimer(null);
      };
    }
  }, [autoSave, hasUnsavedChanges]); // Removed handleSave from dependencies to prevent infinite loops

  // Enhanced loading state with better UX
  if (isLoading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        role="status"
        aria-label="Loading MOOE data"
      >
        <CircularProgress size={60} thickness={4} sx={{ color: "#375e38" }} />
        <Typography variant="h6" sx={{ mt: 2, color: "#375e38" }}>
          Loading MOOE data...
        </Typography>
        <Typography variant="body2" sx={{ mt: 1, color: "text.secondary" }}>
          Please wait while we fetch your budget information
        </Typography>
      </Box>
    );
  }

  // Enhanced error state with retry option
  if (error) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        role="alert"
        aria-label="Error loading data"
      >
        <Typography variant="h6" color="error" sx={{ mb: 2 }}>
          ⚠️ Error loading MOOE data
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: "center" }}>
          {error.response?.data?.message || error.message || "Unable to load data. Please check your connection and try again."}
        </Typography>
        <Button
          variant="contained"
          onClick={() => queryClient.invalidateQueries(["mooeData"])}
          sx={{ backgroundColor: "#375e38", "&:hover": { backgroundColor: "#2d4a2e" } }}
        >
          Retry Loading
        </Button>
      </Box>
    );
  }

  const isEditable = ["Not Submitted", "Returned", "Draft"].includes(
    data?.status
  );

  // Enhanced export functionality with multiple formats
  const handleExportToExcel = () => {
    try {
      // Include both regular MOOE and IAs O&M Cost data
      const regularMooeData = filteredData.flatMap(row =>
        row.children.map(child => ({
          'Category': 'Regular MOOE',
          'Subline Item': row.sublineItem,
          'Accounting Title': child.accountingTitle,
          'UACS Code': child.uacsCode,
          'Income': parseFloat(child.income || 0),
          'Subsidy': parseFloat(child.subsidy || 0),
          'Amount': parseFloat(child.income || 0) + parseFloat(child.subsidy || 0),
          'Status': child.status || 'Not Submitted',
          'Region': activeRegion?.name || 'Unknown',
          'Fiscal Year': data?.fiscalYear || '',
          'Budget Type': data?.budgetType || '',
          'Export Date': new Date().toISOString().split('T')[0]
        }))
      );

      // Add IAs O&M Cost data
      const iasData = [
        {
          'Category': 'IAs O&M Cost',
          'Subline Item': 'IRRIGATORS\' ASSOCIATIONS (IAs) OPERATION & MAINTENANCE COST',
          'Accounting Title': 'NIS (National Irrigation System)',
          'UACS Code': 'NIS',
          'Income': parseFloat(iasOMCost.nis || 0),
          'Subsidy': parseFloat(iasOMCost.nisSubsidy || 0),
          'Amount': parseFloat(iasOMCost.nis || 0) + parseFloat(iasOMCost.nisSubsidy || 0),
          'Status': 'Active',
          'Region': activeRegion?.name || 'Unknown',
          'Fiscal Year': data?.fiscalYear || '',
          'Budget Type': data?.budgetType || '',
          'Export Date': new Date().toISOString().split('T')[0]
        },
        {
          'Category': 'IAs O&M Cost',
          'Subline Item': 'IRRIGATORS\' ASSOCIATIONS (IAs) OPERATION & MAINTENANCE COST',
          'Accounting Title': 'CIS (Communal Irrigation System)',
          'UACS Code': 'CIS',
          'Income': parseFloat(iasOMCost.cis || 0),
          'Subsidy': parseFloat(iasOMCost.cisSubsidy || 0),
          'Amount': parseFloat(iasOMCost.cis || 0) + parseFloat(iasOMCost.cisSubsidy || 0),
          'Status': 'Active',
          'Region': activeRegion?.name || 'Unknown',
          'Fiscal Year': data?.fiscalYear || '',
          'Budget Type': data?.budgetType || '',
          'Export Date': new Date().toISOString().split('T')[0]
        }
      ];

      const exportData = [...regularMooeData, ...iasData];

      if (exportData.length === 0) {
        toast.warning("No data to export");
        return;
      }

      // Create enhanced CSV content with proper formatting
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => {
            const value = row[header];
            // Properly escape values that contain commas or quotes
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      // Download file with enhanced naming
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const regionName = activeRegion?.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'Unknown';
      a.download = `MOOE_Data_${regionName}_${timestamp}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast.success(`Data exported successfully! ${exportData.length} records exported.`);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export data. Please try again.");
    }
  };

  // Import functionality
  const handleImportData = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      toast.error("Please select a CSV file");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target.result;
        const lines = csv.split('\n');
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

        if (!headers.includes('UACS Code') || !headers.includes('Income') || !headers.includes('Subsidy')) {
          toast.error("Invalid CSV format. Required columns: UACS Code, Income, Subsidy");
          return;
        }

        const importedData = [];
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
          const rowData = {};
          headers.forEach((header, index) => {
            rowData[header] = values[index] || '';
          });

          if (rowData['UACS Code'] && (rowData['Income'] || rowData['Subsidy'])) {
            importedData.push(rowData);
          }
        }

        if (importedData.length === 0) {
          toast.warning("No valid data found in the CSV file");
          return;
        }

        // Apply imported data to the current dataset
        queryClient.setQueryData(["mooeData", activeRegion?.id], (old) => {
          if (!old) return old;

          const updatedData = old.formattedData.map(row => ({
            ...row,
            children: row.children.map(child => {
              const importedRow = importedData.find(item => item['UACS Code'] === child.uacsCode);
              if (importedRow) {
                return {
                  ...child,
                  income: importedRow['Income'] || child.income,
                  subsidy: importedRow['Subsidy'] || child.subsidy,
                  amount: (parseFloat(importedRow['Income'] || 0) + parseFloat(importedRow['Subsidy'] || 0)).toString()
                };
              }
              return child;
            })
          }));

          return {
            ...old,
            formattedData: updatedData
          };
        });

        setHasUnsavedChanges(true);
        toast.success(`Successfully imported ${importedData.length} records`);
      } catch (error) {
        console.error("Import error:", error);
        toast.error("Failed to import data. Please check the file format.");
      }
    };

    reader.readAsText(file);
    // Reset the input
    event.target.value = '';
  };

  const handleBulkEdit = () => {
    setBulkEditMode(!bulkEditMode);
    setSelectedRows([]);
  };

  const toggleExpandAll = () => {
    if (expandedRows.length === filteredData.length) {
      setExpandedRows([]);
    } else {
      setExpandedRows(filteredData.map(row => row.id));
    }
  };

  return (
    <>
      {/* Enhanced Toolbar */}
      <Box sx={{ mb: 3 }}>
        {/* Top Row - Main Controls */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <StickyButtons
              handleSave={() => handleSave(false)}
              handleClear={handleClear}
              disabled={!isEditable}
            />

            {/* Auto-save toggle */}
            <FormControlLabel
              control={
                <Switch
                  checked={autoSave}
                  onChange={(e) => setAutoSave(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <SaveIcon fontSize="small" />
                  <Typography variant="body2">Auto-save</Typography>
                </Box>
              }
            />

            {/* Last saved indicator */}
            {lastSaved && (
              <Chip
                size="small"
                label={`Last saved: ${lastSaved.toLocaleTimeString()}`}
                color="success"
                variant="outlined"
              />
            )}

            {/* Unsaved changes indicator */}
            {hasUnsavedChanges && (
              <Chip
                size="small"
                label="Unsaved changes"
                color="warning"
                variant="outlined"
              />
            )}
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {/* View mode toggle */}
            <Tooltip title="Toggle view mode">
              <IconButton
                onClick={() => setViewMode(viewMode === "table" ? "chart" : "table")}
                color={viewMode === "chart" ? "primary" : "default"}
              >
                {viewMode === "table" ? <BarChartIcon /> : <VisibilityIcon />}
              </IconButton>
            </Tooltip>

            {/* Export button */}
            <Tooltip title="Export to CSV">
              <IconButton onClick={handleExportToExcel} color="primary">
                <GetAppIcon />
              </IconButton>
            </Tooltip>

            {/* Import button */}
            <Tooltip title="Import from CSV">
              <IconButton component="label" color="primary" disabled={!isEditable}>
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleImportData}
                  style={{ display: 'none' }}
                />
                <GetAppIcon sx={{ transform: 'rotate(180deg)' }} />
              </IconButton>
            </Tooltip>

            {/* Bulk edit toggle */}
            <Tooltip title="Bulk edit mode">
              <IconButton
                onClick={handleBulkEdit}
                color={bulkEditMode ? "primary" : "default"}
              >
                <EditIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Second Row - Search and Filters */}
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          {/* Enhanced Search with accessibility */}
          <TextField
            size="small"
            placeholder="Search subline items, accounting titles, or UACS codes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300 }}
            aria-label="Search MOOE data"
            inputProps={{
              'aria-describedby': 'search-help-text',
            }}
            helperText={searchTerm && filteredData.length === 0 ? "No results found" : ""}
          />
          <Typography
            id="search-help-text"
            variant="caption"
            sx={{ display: 'none' }}
          >
            Search across subline items, accounting titles, and UACS codes
          </Typography>

          {/* Filter button */}
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={(e) => setFilterAnchorEl(e.currentTarget)}
            color={Object.values(selectedFilters).some(f =>
              Array.isArray(f) ? f.length > 0 : f !== "" && f !== false
            ) ? "primary" : "inherit"}
          >
            Filters
          </Button>

          {/* Expand/Collapse All */}
          <Button
            variant="outlined"
            startIcon={expandedRows.length === filteredData.length ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            onClick={toggleExpandAll}
          >
            {expandedRows.length === filteredData.length ? "Collapse All" : "Expand All"}
          </Button>

          {/* Results count */}
          <Typography variant="body2" color="text.secondary">
            Showing {filteredData.length} of {data?.formattedData?.length || 0} categories
          </Typography>
        </Box>

        {/* Validation errors alert */}
        {Object.keys(validationErrors).length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Please fix the following errors: {Object.values(validationErrors).join(", ")}
          </Alert>
        )}
      </Box>

      {/* Main Table with Zoom Animation */}
      <Zoom in={true} timeout={600}>
        <Box sx={{ position: 'relative' }}>
          <TableContainer
            component={Paper}
            sx={{
              maxHeight: "calc(70vh - 50px)", // Reduced height to make room for footer
              overflow: "auto",
              borderRadius: '8px',
              border: '1px solid rgba(224, 224, 224, 1)',
              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              transition: 'all 0.3s ease',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '4px',
                '&:hover': {
                  background: '#a8a8a8',
                },
              },
            }}
          >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell width="5%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                }}></TableCell>
                <TableCell width="20%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                }}>SUBLINE ITEM</TableCell>
                <TableCell width="25%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                }}>ACCOUNTING TITLE</TableCell>
                <TableCell width="10%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                }}>UACS CODE</TableCell>
                <TableCell width="12%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  textAlign: "right"
                }}>INCOME</TableCell>
                <TableCell width="12%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  textAlign: "right"
                }}>SUBSIDY</TableCell>
                <TableCell width="16%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  textAlign: "right"
                }}>AMOUNT</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData?.map((row, index) => (
                <MooeRow
                  key={row.id}
                  row={row}
                  expanded={expandedRows.includes(row.id)}
                  onToggleExpand={toggleExpandRow}
                  onAmountChange={handleAmountChange}
                  onTitleChange={handleTitleChange}
                  onAddCustomMOOE={addCustomMOOE}
                  calculateTotal={calculateTotal}
                  status={data?.status}
                  onIncomeChange={handleIncomeChange}
                  onSubsidyChange={handleSubsidyChange}
                  disableIncomeInputs={disableIncomeInputs}
                />
              ))}
              
              {/* IRRIGATORS' ASSOCIATIONS (IAs) OPERATION & MAINTENANCE COST section */}
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell colSpan={7} sx={{ fontWeight: 'bold', padding: '16px', borderBottom: '2px solid #375e38' }}>
                  IRRIGATORS' ASSOCIATIONS (IAs) OPERATION & MAINTENANCE COST
                </TableCell>
              </TableRow>
              
              {/* NIS Row */}
              <TableRow>
                <TableCell sx={{ width: '5%' }}></TableCell>
                <TableCell sx={{ width: '20%' }}></TableCell>
                <TableCell sx={{ width: '25%' }}>NIS (National Irrigation System)</TableCell>
                <TableCell sx={{ width: '10%' }}></TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.nis === "0" || iasOMCost.nis === 0 ? "" : (iasOMCost.nis || "")}
                    onChange={handleIAsOMCostChange('nis')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.nisSubsidy === "0" || iasOMCost.nisSubsidy === 0 ? "" : (iasOMCost.nisSubsidy || "")}
                    onChange={handleIAsOMCostChange('nisSubsidy')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '16%', textAlign: 'right' }}>
                  ₱{(parseFloat(iasOMCost.nis || 0) + parseFloat(iasOMCost.nisSubsidy || 0)).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
              </TableRow>
              
              {/* CIS Row */}
              <TableRow>
                <TableCell sx={{ width: '5%' }}></TableCell>
                <TableCell sx={{ width: '20%' }}></TableCell>
                <TableCell sx={{ width: '25%' }}>CIS (Communal Irrigation System)</TableCell>
                <TableCell sx={{ width: '10%' }}></TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.cis === "0" || iasOMCost.cis === 0 ? "" : (iasOMCost.cis || "")}
                    onChange={handleIAsOMCostChange('cis')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.cisSubsidy === "0" || iasOMCost.cisSubsidy === 0 ? "" : (iasOMCost.cisSubsidy || "")}
                    onChange={handleIAsOMCostChange('cisSubsidy')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '16%', textAlign: 'right' }}>
                  ₱{(parseFloat(iasOMCost.cis || 0) + parseFloat(iasOMCost.cisSubsidy || 0)).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
              </TableRow>
              
              {/* IAs O&M Cost Total Row */}
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell colSpan={4} sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  MOOE - O & M COST (IAs):
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  ₱{grandTotals.iasIncomeTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  ₱{grandTotals.iasSubsidyTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  ₱{grandTotals.iasTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>

          {/* Fixed Footer Table with Animation */}
          <Fade in={true} timeout={1000}>
            <Table
              sx={{
                position: 'sticky',
                bottom: 0,
                width: '100%',
                tableLayout: 'fixed',
                borderCollapse: 'separate',
                borderSpacing: 0,
                marginTop: '-2px', // To avoid gap between tables
                zIndex: 2,
                boxShadow: '0 -2px 8px rgba(0,0,0,0.1)',
                transition: 'all 0.3s ease'
              }}
            >
          <TableFooter>
            {/* Regular MOOE Total Row */}
            <TableRow>
              <TableCell 
                colSpan={4} 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "60%",
                }}
              >
                REGULAR MOOE TOTAL:
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.regularIncome.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.regularSubsidy.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "16%",
                }}
              >
                ₱{grandTotals.regularTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
            </TableRow>
            
            {/* IAs O&M Cost Total Row */}
            <TableRow>
              <TableCell 
                colSpan={4} 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "60%",
                }}
              >
                MOOE - O & M COST (IAs):
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.iasIncomeTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.iasSubsidyTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "16%",
                }}
              >
                ₱{grandTotals.iasTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
            </TableRow>
            
            {/* Grand Total Row */}
            <TableRow>
              <TableCell 
                colSpan={4} 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "60%",
                }}
              >
                TOTAL MOOE REGULAR + IAs O & M COST:
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.income.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.subsidy.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "16%",
                }}
              >
                ₱{grandTotals.total.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
            </TableRow>
          </TableFooter>
            </Table>
          </Fade>
        </Box>
      </Zoom>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={() => setFilterAnchorEl(null)}
        PaperProps={{ sx: { minWidth: 300, p: 2 } }}
      >
        <Typography variant="h6" gutterBottom>Filters</Typography>
        <Divider sx={{ mb: 2 }} />

        {/* Amount Range Filter */}
        <Typography variant="subtitle2" gutterBottom>Amount Range</Typography>
        <Box display="flex" gap={1} mb={2}>
          <TextField
            size="small"
            label="Min"
            type="number"
            value={selectedFilters.amountRange.min}
            onChange={(e) => setSelectedFilters(prev => ({
              ...prev,
              amountRange: { ...prev.amountRange, min: e.target.value }
            }))}
          />
          <TextField
            size="small"
            label="Max"
            type="number"
            value={selectedFilters.amountRange.max}
            onChange={(e) => setSelectedFilters(prev => ({
              ...prev,
              amountRange: { ...prev.amountRange, max: e.target.value }
            }))}
          />
        </Box>

        {/* Value Filters */}
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={selectedFilters.hasValues}
                onChange={(e) => setSelectedFilters(prev => ({
                  ...prev,
                  hasValues: e.target.checked,
                  emptyValues: e.target.checked ? false : prev.emptyValues
                }))}
              />
            }
            label="Show only entries with values"
          />          <FormControlLabel
            control={
              <Checkbox
                checked={selectedFilters.emptyValues}
                onChange={(e) => setSelectedFilters(prev => ({
                  ...prev,
                  emptyValues: e.target.checked,
                  hasValues: e.target.checked ? false : prev.hasValues
                }))}
              />
            }
            label="Show only empty entries"
          />
        </FormGroup>

        <Box display="flex" justifyContent="space-between" mt={2}>
          <Button
            size="small"
            onClick={() => setSelectedFilters({
              sublineItems: [],
              amountRange: { min: "", max: "" },
              hasValues: false,
              emptyValues: false
            })}
          >
            Clear All
          </Button>
          <Button
            size="small"
            variant="contained"
            onClick={() => setFilterAnchorEl(null)}
          >
            Apply
          </Button>
        </Box>
      </Menu>

      {/* Enhanced Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </>
  );
};

export default Mooe;
